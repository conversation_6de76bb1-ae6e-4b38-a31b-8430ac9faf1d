<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <None Include="..\..\AURACRON.uproject" />
    <Filter Include="Source">
      <UniqueIdentifier>{F31BBDD1-B3E8-3BCC-9652-680E16935819}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\AURACRON.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\Source\AURACRONEditor.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\.vsconfig" />
    <None Include="..\..\AURACRON.uplugin" />
    <None Include="..\..\AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md" />
    <None Include="..\..\AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED_EN.md" />
    <None Include="..\..\AURACRON_SIGILVFXMANAGER_AUDIT_REPORT.md" />
    <None Include="..\..\AURACRON_SIGIL_MANAGER_AUDIT_REPORT.md" />
    <None Include="..\..\build_output.txt" />
    <None Include="..\..\IMPLEMENTACAO_FFASTARRAYSERIALIZER_UE56_COMPLETA.md" />
    <Filter Include="Config">
      <UniqueIdentifier>{FA535FFB-25E1-3D20-B416-52F9BE21E06E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Config\DefaultEditor.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEngine.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGame.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGameplayTags.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultInput.ini">
      <Filter>Config</Filter>
    </None>
    <Filter Include="Source\AURACRON">
      <UniqueIdentifier>{38706F87-0F5F-30F3-ACA7-4436234CDD96}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\AURACRON\AURACRON.Build.cs">
      <Filter>Source\AURACRON</Filter>
    </None>
    <Filter Include="Source\AURACRON\Private">
      <UniqueIdentifier>{E7F264DC-6C17-3666-B3EC-EA0F417E010B}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\AURACRON.cpp">
      <Filter>Source\AURACRON\Private</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Character">
      <UniqueIdentifier>{232CC4EC-8A62-3B3F-8933-BA262E96930D}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Character\AURACRONCharacter.cpp">
      <Filter>Source\AURACRON\Private\Character</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Components">
      <UniqueIdentifier>{AD03358A-45B8-3156-8B3E-914ED3025A09}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Components\AURACRONMovementComponent.cpp">
      <Filter>Source\AURACRON\Private\Components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Components\AURACRONSigilComponent.cpp">
      <Filter>Source\AURACRON\Private\Components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Components\DamageZoneComponent.cpp">
      <Filter>Source\AURACRON\Private\Components</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Debug">
      <UniqueIdentifier>{5CA211E5-903D-3F0E-B6FA-F605AF169081}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Debug\SigilDebugCommands.cpp">
      <Filter>Source\AURACRON\Private\Debug</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Fusion">
      <UniqueIdentifier>{B9FD29CB-A148-31BF-9CA5-1E7ED1610E91}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Fusion\SigilFusionSystem.cpp">
      <Filter>Source\AURACRON\Private\Fusion</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\GAS">
      <UniqueIdentifier>{41191BA3-4668-34E1-B493-2E2E4C8A4B7D}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\GAS\AURACRONAttributeSet.cpp">
      <Filter>Source\AURACRON\Private\GAS</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Multiplayer">
      <UniqueIdentifier>{87751589-A20E-3B72-B70A-6F50BDDFBEE6}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Multiplayer\SigilNetworkConfig.cpp">
      <Filter>Source\AURACRON\Private\Multiplayer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Multiplayer\SigilReplicationManager.cpp">
      <Filter>Source\AURACRON\Private\Multiplayer</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\PCG">
      <UniqueIdentifier>{0B496708-3007-3A5C-ACD7-92AD8C4EA817}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONMapMeasurements.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGArsenalIsland.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGChaosIsland.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGChaosIslandManager.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGChaosPortal.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGEnergyPulse.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGEnvironment.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGEnvironmentManager.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGIsland.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGJungleSystem.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGLaneSystem.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGMathLibrary.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGNexusIsland.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGObjectiveSystem.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGPerformanceManager.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGPhaseManager.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGPortal.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGPrismalFlow.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGPurgatoryAnchor.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGSanctuaryIsland.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGShadowNexuses.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGSpectralGuardian.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGSubsystem.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGTowersOfLamentation.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGTrail.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGUtility.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\PCG\AURACRONPCGWorldPartitionIntegration.cpp">
      <Filter>Source\AURACRON\Private\PCG</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\Sigils">
      <UniqueIdentifier>{80360CA7-8B2F-39AA-B92B-398EE52BBAF6}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilAbilities.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilAbilityEffects.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilAttributeSet.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilGameplayEffects.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilItem.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\Sigils\SigilManagerComponent.cpp">
      <Filter>Source\AURACRON\Private\Sigils</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\UI">
      <UniqueIdentifier>{BB7EA195-13C7-3081-BB04-EEA3AF0198F8}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\UI\SigilWidgets.cpp">
      <Filter>Source\AURACRON\Private\UI</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Private\VFX">
      <UniqueIdentifier>{8790BEC4-8721-3863-A2D4-D0B14F220986}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\VFX\SigilVFXManager.cpp">
      <Filter>Source\AURACRON\Private\VFX</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Public">
      <UniqueIdentifier>{30D56F11-4DA0-389E-A273-B3AD9FE16C61}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source\AURACRON\Public\Character">
      <UniqueIdentifier>{369CA8C1-C647-397F-B444-1FB111C2DFCC}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Character\AURACRONCharacter.h">
      <Filter>Source\AURACRON\Public\Character</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Components">
      <UniqueIdentifier>{C4859FE7-32E4-34CE-83E1-4536C4963E6C}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Components\AURACRONMovementComponent.h">
      <Filter>Source\AURACRON\Public\Components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Components\AURACRONSigilComponent.h">
      <Filter>Source\AURACRON\Public\Components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Components\DamageZoneComponent.h">
      <Filter>Source\AURACRON\Public\Components</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Data">
      <UniqueIdentifier>{1C9C9DC6-8DE6-39CB-B9EB-7066C283BBA9}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Data\AURACRONEnums.h">
      <Filter>Source\AURACRON\Public\Data</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Data\AURACRONStructs.h">
      <Filter>Source\AURACRON\Public\Data</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Debug">
      <UniqueIdentifier>{933836EB-647E-3963-9D86-9BDA2A7952CB}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Debug\SigilDebugCommands.h">
      <Filter>Source\AURACRON\Public\Debug</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Fusion">
      <UniqueIdentifier>{1F4ABCA1-0E40-3D52-90E7-908958832904}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Fusion\SigilFusionSystem.h">
      <Filter>Source\AURACRON\Public\Fusion</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\GAS">
      <UniqueIdentifier>{19D43689-6383-3D34-ADED-98AC5A4352CF}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\GAS\AURACRONAttributeSet.h">
      <Filter>Source\AURACRON\Public\GAS</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Multiplayer">
      <UniqueIdentifier>{CFE59D4B-D59F-3613-8909-2394124C68D8}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Multiplayer\SigilNetworkConfig.h">
      <Filter>Source\AURACRON\Public\Multiplayer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Multiplayer\SigilReplicationManager.h">
      <Filter>Source\AURACRON\Public\Multiplayer</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\PCG">
      <UniqueIdentifier>{83A78911-B12E-33DE-AF9F-9C42AA79184F}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONMapMeasurements.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGArsenalIsland.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGChaosIsland.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGChaosIslandManager.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGChaosPortal.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGEnergyPulse.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGEnvironment.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGEnvironmentManager.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGIsland.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGJungleSystem.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGLaneSystem.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGMathLibrary.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGNexusIsland.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGObjectiveSystem.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGPerformanceManager.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGPhaseManager.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGPortal.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGPrismalFlow.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGSanctuaryIsland.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGSubsystem.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGTrail.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGTypes.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGUtility.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\PCG\AURACRONPCGWorldPartitionIntegration.h">
      <Filter>Source\AURACRON\Public\PCG</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\Sigils">
      <UniqueIdentifier>{AEFD65CA-2884-3F0F-ABD0-9C39B6E6C900}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilAbilities.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilAbilityEffects.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilAttributeSet.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilGameplayEffects.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilItem.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\Sigils\SigilManagerComponent.h">
      <Filter>Source\AURACRON\Public\Sigils</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\UI">
      <UniqueIdentifier>{2A483FF3-3116-36FC-A396-CAE6D21B7877}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\UI\SigilWidgets.h">
      <Filter>Source\AURACRON\Public\UI</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Public\VFX">
      <UniqueIdentifier>{1F5507C3-37EA-343D-B06B-FAC882ABAD30}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\VFX\SigilVFXManager.h">
      <Filter>Source\AURACRON\Public\VFX</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
