// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "AURACRONStructs.h"
#include "Engine/TimerHandle.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGUtility.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGObjectiveSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGIsland_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONBuffType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandSector();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMeshComponentArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveInfo();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGActorReferences();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONProceduralObjective();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONObjectiveInfo ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo;
class UScriptStruct* FAURACRONObjectiveInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONObjectiveInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de um objetivo estrat\xc3\xa9gico\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de um objetivo estrat\xc3\xa9gico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveType_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionsByEnvironment_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o do objetivo para cada ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o do objetivo para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRadius_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do pit/\xc3\xa1rea do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do pit/\xc3\xa1rea do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PitDepth_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Profundidade do pit (se aplic\xc3\xa1vel) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profundidade do pit (se aplic\xc3\xa1vel)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de respawn em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de respawn em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeUntilRespawn_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** HP do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HP do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** HP atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HP atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time que capturou o objetivo (0=Team1, 1=Team2, -1=Neutro) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time que capturou o objetivo (0=Team1, 1=Team2, -1=Neutro)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveBuffs_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Buffs fornecidos pelo objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Buffs fornecidos pelo objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveActor_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ator do objetivo no mundo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator do objetivo no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffDuration_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o dos buffs em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o dos buffs em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActiveInCurrentPhase_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo est\xc3\xa1 ativo na fase atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo est\xc3\xa1 ativo na fase atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumPhaseForActivation_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase m\xc3\xadnima para ativa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase m\xc3\xadnima para ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaptureTime_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de captura em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de captura em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o no mundo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffStrength_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""a do buff fornecido */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a do buff fornecido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualEffectScale_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala do efeito visual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala do efeito visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectColor_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de mesh do objetivo */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de mesh do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vida atual do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vida atual do objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PositionsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PositionsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PositionsByEnvironment;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PitDepth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeUntilRespawn;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveBuffs_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveBuffs_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ObjectiveBuffs;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BuffDuration;
	static void NewProp_bIsActiveInCurrentPhase_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActiveInCurrentPhase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumPhaseForActivation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumPhaseForActivation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CaptureTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BuffStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisualEffectScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectColor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONObjectiveInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveType_MetaData), NewProp_ObjectiveType_MetaData) }; // 2271266485
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_ValueProp = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp = { "PositionsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, PositionsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionsByEnvironment_MetaData), NewProp_PositionsByEnvironment_MetaData) }; // 2509470107
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, CurrentState), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 1111886678
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveRadius = { "ObjectiveRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRadius_MetaData), NewProp_ObjectiveRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PitDepth = { "PitDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, PitDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PitDepth_MetaData), NewProp_PitDepth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_TimeUntilRespawn = { "TimeUntilRespawn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, TimeUntilRespawn), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeUntilRespawn_MetaData), NewProp_TimeUntilRespawn_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_ValueProp = { "ObjectiveBuffs", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_Key_KeyProp = { "ObjectiveBuffs_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs = { "ObjectiveBuffs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveBuffs), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveBuffs_MetaData), NewProp_ObjectiveBuffs_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONObjectiveInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONObjectiveInfo), &Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveActor = { "ObjectiveActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveActor_MetaData), NewProp_ObjectiveActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_BuffDuration = { "BuffDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, BuffDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffDuration_MetaData), NewProp_BuffDuration_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase_SetBit(void* Obj)
{
	((FAURACRONObjectiveInfo*)Obj)->bIsActiveInCurrentPhase = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase = { "bIsActiveInCurrentPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONObjectiveInfo), &Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActiveInCurrentPhase_MetaData), NewProp_bIsActiveInCurrentPhase_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation = { "MinimumPhaseForActivation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, MinimumPhaseForActivation), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumPhaseForActivation_MetaData), NewProp_MinimumPhaseForActivation_MetaData) }; // 2541365769
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CaptureTime = { "CaptureTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, CaptureTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaptureTime_MetaData), NewProp_CaptureTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_BuffStrength = { "BuffStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, BuffStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffStrength_MetaData), NewProp_BuffStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_VisualEffectScale = { "VisualEffectScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, VisualEffectScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualEffectScale_MetaData), NewProp_VisualEffectScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_EffectColor = { "EffectColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, EffectColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectColor_MetaData), NewProp_EffectColor_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PitDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_TimeUntilRespawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_BuffDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CaptureTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_BuffStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_VisualEffectScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_EffectColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_Health,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONObjectiveInfo",
	Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers),
	sizeof(FAURACRONObjectiveInfo),
	alignof(FAURACRONObjectiveInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONObjectiveInfo **********************************************

// ********** Begin Delegate FOnObjectiveCreated ***************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveCreated_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Delegates consolidados para eventos de objetivos\n * CONSOLIDADO: Combina delegates de ambos os sistemas\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates consolidados para eventos de objetivos\nCONSOLIDADO: Combina delegates de ambos os sistemas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objective;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::NewProp_Objective = { "Objective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveCreated_Parms, Objective), Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objective_MetaData), NewProp_Objective_MetaData) }; // 3242810244
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::NewProp_Objective,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveCreated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveCreated_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCreated, FAURACRONProceduralObjective const& Objective)
{
	struct _Script_AURACRON_eventOnObjectiveCreated_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
	_Script_AURACRON_eventOnObjectiveCreated_Parms Parms;
	Parms.Objective=Objective;
	OnObjectiveCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveCreated *****************************************************

// ********** Begin Delegate FOnObjectiveDestroyed *************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveDestroyed_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objective;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::NewProp_Objective = { "Objective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveDestroyed_Parms, Objective), Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objective_MetaData), NewProp_Objective_MetaData) }; // 3242810244
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::NewProp_Objective,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveDestroyed__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveDestroyed, FAURACRONProceduralObjective const& Objective)
{
	struct _Script_AURACRON_eventOnObjectiveDestroyed_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
	_Script_AURACRON_eventOnObjectiveDestroyed_Parms Parms;
	Parms.Objective=Objective;
	OnObjectiveDestroyed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveDestroyed ***************************************************

// ********** Begin Delegate FOnObjectiveStateChanged **********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveStateChanged_Parms
	{
		FAURACRONProceduralObjective Objective;
		EAURACRONObjectiveState OldState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objective;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_Objective = { "Objective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveStateChanged_Parms, Objective), Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objective_MetaData), NewProp_Objective_MetaData) }; // 3242810244
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveStateChanged_Parms, OldState), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(0, nullptr) }; // 1111886678
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_Objective,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveStateChanged, FAURACRONProceduralObjective const& Objective, EAURACRONObjectiveState OldState)
{
	struct _Script_AURACRON_eventOnObjectiveStateChanged_Parms
	{
		FAURACRONProceduralObjective Objective;
		EAURACRONObjectiveState OldState;
	};
	_Script_AURACRON_eventOnObjectiveStateChanged_Parms Parms;
	Parms.Objective=Objective;
	Parms.OldState=OldState;
	OnObjectiveStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveStateChanged ************************************************

// ********** Begin Delegate FOnObjectiveCaptured **************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveCaptured_Parms
	{
		int32 ObjectiveIndex;
		int32 CapturingTeam;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CapturingTeam;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveCaptured_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_CapturingTeam = { "CapturingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveCaptured_Parms, CapturingTeam), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_CapturingTeam,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveCaptured__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCaptured_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCaptured_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveCaptured_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCaptured, int32 ObjectiveIndex, int32 CapturingTeam)
{
	struct _Script_AURACRON_eventOnObjectiveCaptured_Parms
	{
		int32 ObjectiveIndex;
		int32 CapturingTeam;
	};
	_Script_AURACRON_eventOnObjectiveCaptured_Parms Parms;
	Parms.ObjectiveIndex=ObjectiveIndex;
	Parms.CapturingTeam=CapturingTeam;
	OnObjectiveCaptured.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveCaptured ****************************************************

// ********** Begin Delegate FOnChaosIslandEvent ***************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnChaosIslandEvent_Parms
	{
		int32 IslandIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_IslandIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::NewProp_IslandIndex = { "IslandIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnChaosIslandEvent_Parms, IslandIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::NewProp_IslandIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnChaosIslandEvent__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::_Script_AURACRON_eventOnChaosIslandEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::_Script_AURACRON_eventOnChaosIslandEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnChaosIslandEvent_DelegateWrapper(const FMulticastScriptDelegate& OnChaosIslandEvent, int32 IslandIndex)
{
	struct _Script_AURACRON_eventOnChaosIslandEvent_Parms
	{
		int32 IslandIndex;
	};
	_Script_AURACRON_eventOnChaosIslandEvent_Parms Parms;
	Parms.IslandIndex=IslandIndex;
	OnChaosIslandEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChaosIslandEvent *****************************************************

// ********** Begin Delegate FOnObjectiveBuffApplied ***********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveBuffApplied_Parms
	{
		int32 TeamIndex;
		EAURACRONBuffType BuffType;
		float Magnitude;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BuffType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BuffType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Magnitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_TeamIndex = { "TeamIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveBuffApplied_Parms, TeamIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_BuffType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_BuffType = { "BuffType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveBuffApplied_Parms, BuffType), Z_Construct_UEnum_AURACRON_EAURACRONBuffType, METADATA_PARAMS(0, nullptr) }; // 362549284
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_Magnitude = { "Magnitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveBuffApplied_Parms, Magnitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveBuffApplied_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_TeamIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_BuffType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_BuffType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_Magnitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveBuffApplied__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveBuffApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveBuffApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveBuffApplied_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveBuffApplied, int32 TeamIndex, EAURACRONBuffType BuffType, float Magnitude, float Duration)
{
	struct _Script_AURACRON_eventOnObjectiveBuffApplied_Parms
	{
		int32 TeamIndex;
		EAURACRONBuffType BuffType;
		float Magnitude;
		float Duration;
	};
	_Script_AURACRON_eventOnObjectiveBuffApplied_Parms Parms;
	Parms.TeamIndex=TeamIndex;
	Parms.BuffType=BuffType;
	Parms.Magnitude=Magnitude;
	Parms.Duration=Duration;
	OnObjectiveBuffApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveBuffApplied *************************************************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function AddAnchorEffects ********************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics
{
	struct AURACRONPCGObjectiveSystem_eventAddAnchorEffects_Parms
	{
		int32 ObjectiveIndex;
		UStaticMeshComponent* MeshComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Objectives|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adicionar efeitos visuais para Anchors */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar efeitos visuais para Anchors" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAddAnchorEffects_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAddAnchorEffects_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::NewProp_MeshComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "AddAnchorEffects", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::AURACRONPCGObjectiveSystem_eventAddAnchorEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::AURACRONPCGObjectiveSystem_eventAddAnchorEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execAddAnchorEffects)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddAnchorEffects(Z_Param_ObjectiveIndex,Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function AddAnchorEffects **********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function AddPrismalNexusEffects **************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics
{
	struct AURACRONPCGObjectiveSystem_eventAddPrismalNexusEffects_Parms
	{
		int32 ObjectiveIndex;
		UStaticMeshComponent* MeshComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Objectives|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adicionar efeitos visuais para Prismal Nexus */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar efeitos visuais para Prismal Nexus" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAddPrismalNexusEffects_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAddPrismalNexusEffects_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::NewProp_MeshComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "AddPrismalNexusEffects", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::AURACRONPCGObjectiveSystem_eventAddPrismalNexusEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::AURACRONPCGObjectiveSystem_eventAddPrismalNexusEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execAddPrismalNexusEffects)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddPrismalNexusEffects(Z_Param_ObjectiveIndex,Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function AddPrismalNexusEffects ****************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function AddStormCoreEffects *****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics
{
	struct AURACRONPCGObjectiveSystem_eventAddStormCoreEffects_Parms
	{
		int32 ObjectiveIndex;
		UStaticMeshComponent* MeshComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Objectives|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adicionar efeitos visuais para Storm Core */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar efeitos visuais para Storm Core" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAddStormCoreEffects_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAddStormCoreEffects_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::NewProp_MeshComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "AddStormCoreEffects", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::AURACRONPCGObjectiveSystem_eventAddStormCoreEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::AURACRONPCGObjectiveSystem_eventAddStormCoreEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execAddStormCoreEffects)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddStormCoreEffects(Z_Param_ObjectiveIndex,Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function AddStormCoreEffects *******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function ApplyEnvironmentPrismalFlowIntegration 
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics
{
	struct AURACRONPCGObjectiveSystem_eventApplyEnvironmentPrismalFlowIntegration_Parms
	{
		int32 ObjectiveIndex;
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar integra\xc3\xa7\xc3\xa3o de Fluxo Prismal ao objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar integra\xc3\xa7\xc3\xa3o de Fluxo Prismal ao objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventApplyEnvironmentPrismalFlowIntegration_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventApplyEnvironmentPrismalFlowIntegration_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "ApplyEnvironmentPrismalFlowIntegration", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::AURACRONPCGObjectiveSystem_eventApplyEnvironmentPrismalFlowIntegration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::AURACRONPCGObjectiveSystem_eventApplyEnvironmentPrismalFlowIntegration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execApplyEnvironmentPrismalFlowIntegration)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyEnvironmentPrismalFlowIntegration(Z_Param_ObjectiveIndex,EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function ApplyEnvironmentPrismalFlowIntegration 

// ********** Begin Class AAURACRONPCGObjectiveSystem Function ApplyEnvironmentTrailsIntegration ***
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics
{
	struct AURACRONPCGObjectiveSystem_eventApplyEnvironmentTrailsIntegration_Parms
	{
		int32 ObjectiveIndex;
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar integra\xc3\xa7\xc3\xa3o de trilhos ao objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar integra\xc3\xa7\xc3\xa3o de trilhos ao objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventApplyEnvironmentTrailsIntegration_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventApplyEnvironmentTrailsIntegration_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "ApplyEnvironmentTrailsIntegration", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::AURACRONPCGObjectiveSystem_eventApplyEnvironmentTrailsIntegration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::AURACRONPCGObjectiveSystem_eventApplyEnvironmentTrailsIntegration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execApplyEnvironmentTrailsIntegration)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyEnvironmentTrailsIntegration(Z_Param_ObjectiveIndex,EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function ApplyEnvironmentTrailsIntegration *****

// ********** Begin Class AAURACRONPCGObjectiveSystem Function ApplyIslandControlEffects ***********
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics
{
	struct AURACRONPCGObjectiveSystem_eventApplyIslandControlEffects_Parms
	{
		float ControlPercentage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar efeitos de controle da ilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos de controle da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ControlPercentage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::NewProp_ControlPercentage = { "ControlPercentage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventApplyIslandControlEffects_Parms, ControlPercentage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::NewProp_ControlPercentage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "ApplyIslandControlEffects", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::AURACRONPCGObjectiveSystem_eventApplyIslandControlEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::AURACRONPCGObjectiveSystem_eventApplyIslandControlEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execApplyIslandControlEffects)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ControlPercentage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyIslandControlEffects(Z_Param_ControlPercentage);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function ApplyIslandControlEffects *************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function ApplyTrailPowerToActors *************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics
{
	struct AURACRONPCGObjectiveSystem_eventApplyTrailPowerToActors_Parms
	{
		TArray<AAURACRONPCGTrail*> Trails;
		float Power;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar poder aos trilhos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar poder aos trilhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Trails_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Trails_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Trails;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Power;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::NewProp_Trails_Inner = { "Trails", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGTrail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::NewProp_Trails = { "Trails", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventApplyTrailPowerToActors_Parms, Trails), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Trails_MetaData), NewProp_Trails_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::NewProp_Power = { "Power", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventApplyTrailPowerToActors_Parms, Power), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::NewProp_Trails_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::NewProp_Trails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::NewProp_Power,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "ApplyTrailPowerToActors", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::AURACRONPCGObjectiveSystem_eventApplyTrailPowerToActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::AURACRONPCGObjectiveSystem_eventApplyTrailPowerToActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execApplyTrailPowerToActors)
{
	P_GET_TARRAY_REF(AAURACRONPCGTrail*,Z_Param_Out_Trails);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Power);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTrailPowerToActors(Z_Param_Out_Trails,Z_Param_Power);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function ApplyTrailPowerToActors ***************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function AttackObjective *********************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics
{
	struct AURACRONPCGObjectiveSystem_eventAttackObjective_Parms
	{
		int32 ObjectiveIndex;
		float Damage;
		int32 AttackingTeam;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atacar objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atacar objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AttackingTeam;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms, Damage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_AttackingTeam = { "AttackingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms, AttackingTeam), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGObjectiveSystem_eventAttackObjective_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms), &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_AttackingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "AttackObjective", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::AURACRONPCGObjectiveSystem_eventAttackObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::AURACRONPCGObjectiveSystem_eventAttackObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execAttackObjective)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damage);
	P_GET_PROPERTY(FIntProperty,Z_Param_AttackingTeam);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AttackObjective(Z_Param_ObjectiveIndex,Z_Param_Damage,Z_Param_AttackingTeam);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function AttackObjective ***********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function CalculateIslandControlFromSectors ***
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics
{
	struct AURACRONPCGObjectiveSystem_eventCalculateIslandControlFromSectors_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular controle da ilha baseado nos setores */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular controle da ilha baseado nos setores" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCalculateIslandControlFromSectors_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "CalculateIslandControlFromSectors", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::AURACRONPCGObjectiveSystem_eventCalculateIslandControlFromSectors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::AURACRONPCGObjectiveSystem_eventCalculateIslandControlFromSectors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execCalculateIslandControlFromSectors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateIslandControlFromSectors();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function CalculateIslandControlFromSectors *****

// ********** Begin Class AAURACRONPCGObjectiveSystem Function CalculatePrismalFlowIntensityFromObjectives 
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics
{
	struct AURACRONPCGObjectiveSystem_eventCalculatePrismalFlowIntensityFromObjectives_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular intensidade do Fluxo Prismal baseado nos objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular intensidade do Fluxo Prismal baseado nos objetivos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCalculatePrismalFlowIntensityFromObjectives_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "CalculatePrismalFlowIntensityFromObjectives", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::AURACRONPCGObjectiveSystem_eventCalculatePrismalFlowIntensityFromObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::AURACRONPCGObjectiveSystem_eventCalculatePrismalFlowIntensityFromObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execCalculatePrismalFlowIntensityFromObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculatePrismalFlowIntensityFromObjectives();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function CalculatePrismalFlowIntensityFromObjectives 

// ********** Begin Class AAURACRONPCGObjectiveSystem Function CalculateTrailPowerFromObjectives ***
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics
{
	struct AURACRONPCGObjectiveSystem_eventCalculateTrailPowerFromObjectives_Parms
	{
		EAURACRONTrailType TrailType;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calcular poder dos trilhos baseado nos objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular poder dos trilhos baseado nos objetivos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TrailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TrailType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::NewProp_TrailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::NewProp_TrailType = { "TrailType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCalculateTrailPowerFromObjectives_Parms, TrailType), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(0, nullptr) }; // 2049964576
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCalculateTrailPowerFromObjectives_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::NewProp_TrailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::NewProp_TrailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "CalculateTrailPowerFromObjectives", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::AURACRONPCGObjectiveSystem_eventCalculateTrailPowerFromObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::AURACRONPCGObjectiveSystem_eventCalculateTrailPowerFromObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execCalculateTrailPowerFromObjectives)
{
	P_GET_ENUM(EAURACRONTrailType,Z_Param_TrailType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTrailPowerFromObjectives(EAURACRONTrailType(Z_Param_TrailType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function CalculateTrailPowerFromObjectives *****

// ********** Begin Class AAURACRONPCGObjectiveSystem Function CaptureObjective ********************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics
{
	struct AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms
	{
		int32 ObjectiveIndex;
		int32 CapturingTeam;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Capturar objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Capturar objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CapturingTeam;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_CapturingTeam = { "CapturingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms, CapturingTeam), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms), &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_CapturingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "CaptureObjective", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execCaptureObjective)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_CapturingTeam);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CaptureObjective(Z_Param_ObjectiveIndex,Z_Param_CapturingTeam);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function CaptureObjective **********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function ForceGenerateObjective **************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics
{
	struct AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms
	{
		EAURACRONObjectiveType ObjectiveType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar gera\xc3\xa7\xc3\xa3o de objetivo procedural */" },
#endif
		{ "CPP_Default_ObjectiveType", "None" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar gera\xc3\xa7\xc3\xa3o de objetivo procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2271266485
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "ForceGenerateObjective", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execForceGenerateObjective)
{
	P_GET_ENUM(EAURACRONObjectiveType,Z_Param_ObjectiveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceGenerateObjective(EAURACRONObjectiveType(Z_Param_ObjectiveType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function ForceGenerateObjective ****************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GenerateObjectives ******************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar todos os objetivos estrat\xc3\xa9gicos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar todos os objetivos estrat\xc3\xa9gicos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GenerateObjectives", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGenerateObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateObjectives();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GenerateObjectives ********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GenerateObjectivesForEnvironment ****
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar objetivos para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar objetivos para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GenerateObjectivesForEnvironment", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGenerateObjectivesForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateObjectivesForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GenerateObjectivesForEnvironment ******

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetAllObjectives ********************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms
	{
		TArray<FAURACRONObjectiveInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter informa\xc3\xa7\xc3\xb5""es de todos os objetivos estrat\xc3\xa9gicos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter informa\xc3\xa7\xc3\xb5""es de todos os objetivos estrat\xc3\xa9gicos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 165882
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 165882
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetAllObjectives", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetAllObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONObjectiveInfo>*)Z_Param__Result=P_THIS->GetAllObjectives();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetAllObjectives **********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetAllProceduralObjectives **********
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms
	{
		TArray<FAURACRONProceduralObjective> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todos os objetivos procedurais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todos os objetivos procedurais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 3242810244
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3242810244
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetAllProceduralObjectives", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetAllProceduralObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONProceduralObjective>*)Z_Param__Result=P_THIS->GetAllProceduralObjectives();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetAllProceduralObjectives ************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetObjectiveBuffs *******************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms
	{
		int32 ObjectiveIndex;
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter buffs de um objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter buffs de um objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetObjectiveBuffs", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetObjectiveBuffs)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetObjectiveBuffs(Z_Param_ObjectiveIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetObjectiveBuffs *********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetObjectivesByState ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms
	{
		EAURACRONObjectiveState State;
		TArray<FAURACRONObjectiveInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter objetivos por estado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter objetivos por estado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms, State), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(0, nullptr) }; // 1111886678
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 165882
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 165882
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetObjectivesByState", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetObjectivesByState)
{
	P_GET_ENUM(EAURACRONObjectiveState,Z_Param_State);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONObjectiveInfo>*)Z_Param__Result=P_THIS->GetObjectivesByState(EAURACRONObjectiveState(Z_Param_State));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetObjectivesByState ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetObjectivesByType *****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms
	{
		EAURACRONObjectiveType ObjectiveType;
		TArray<FAURACRONObjectiveInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter objetivos por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter objetivos por tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2271266485
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 165882
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 165882
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetObjectivesByType", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetObjectivesByType)
{
	P_GET_ENUM(EAURACRONObjectiveType,Z_Param_ObjectiveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONObjectiveInfo>*)Z_Param__Result=P_THIS->GetObjectivesByType(EAURACRONObjectiveType(Z_Param_ObjectiveType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetObjectivesByType *******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetStencilValueForObjectiveType *****
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetStencilValueForObjectiveType_Parms
	{
		EAURACRONObjectiveType ObjectiveType;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG|Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fun\xc3\xa7\xc3\xa3o para obter valor de stencil baseado no tipo de objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xa3o para obter valor de stencil baseado no tipo de objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetStencilValueForObjectiveType_Parms, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2271266485
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetStencilValueForObjectiveType_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetStencilValueForObjectiveType", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::AURACRONPCGObjectiveSystem_eventGetStencilValueForObjectiveType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::AURACRONPCGObjectiveSystem_eventGetStencilValueForObjectiveType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetStencilValueForObjectiveType)
{
	P_GET_ENUM(EAURACRONObjectiveType,Z_Param_ObjectiveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetStencilValueForObjectiveType(EAURACRONObjectiveType(Z_Param_ObjectiveType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetStencilValueForObjectiveType *******

// ********** Begin Class AAURACRONPCGObjectiveSystem Function InitializeCentralAuracronIslandIntegration 
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeCentralAuracronIslandIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar integra\xc3\xa7\xc3\xa3o com Ilha Central Auracron */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar integra\xc3\xa7\xc3\xa3o com Ilha Central Auracron" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeCentralAuracronIslandIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "InitializeCentralAuracronIslandIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeCentralAuracronIslandIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeCentralAuracronIslandIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeCentralAuracronIslandIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeCentralAuracronIslandIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execInitializeCentralAuracronIslandIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeCentralAuracronIslandIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function InitializeCentralAuracronIslandIntegration 

// ********** Begin Class AAURACRONPCGObjectiveSystem Function InitializeIslandSectors *************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics
{
	struct AURACRONPCGObjectiveSystem_eventInitializeIslandSectors_Parms
	{
		AAURACRONPCGIsland* Island;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar setores da ilha */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar setores da ilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Island;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::NewProp_Island = { "Island", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventInitializeIslandSectors_Parms, Island), Z_Construct_UClass_AAURACRONPCGIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::NewProp_Island,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "InitializeIslandSectors", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::AURACRONPCGObjectiveSystem_eventInitializeIslandSectors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::AURACRONPCGObjectiveSystem_eventInitializeIslandSectors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execInitializeIslandSectors)
{
	P_GET_OBJECT(AAURACRONPCGIsland,Z_Param_Island);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeIslandSectors(Z_Param_Island);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function InitializeIslandSectors ***************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function InitializeObjectiveSystemAsync ******
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeObjectiveSystemAsync_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializa\xc3\xa7\xc3\xa3o ass\xc3\xadncrona do sistema de objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializa\xc3\xa7\xc3\xa3o ass\xc3\xadncrona do sistema de objetivos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeObjectiveSystemAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "InitializeObjectiveSystemAsync", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeObjectiveSystemAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeObjectiveSystemAsync_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeObjectiveSystemAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeObjectiveSystemAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execInitializeObjectiveSystemAsync)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeObjectiveSystemAsync();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function InitializeObjectiveSystemAsync ********

// ********** Begin Class AAURACRONPCGObjectiveSystem Function InitializePrismalFlowIntegration ****
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializePrismalFlowIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar integra\xc3\xa7\xc3\xa3o com Fluxo Prismal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar integra\xc3\xa7\xc3\xa3o com Fluxo Prismal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializePrismalFlowIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "InitializePrismalFlowIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializePrismalFlowIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializePrismalFlowIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializePrismalFlowIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializePrismalFlowIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execInitializePrismalFlowIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePrismalFlowIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function InitializePrismalFlowIntegration ******

// ********** Begin Class AAURACRONPCGObjectiveSystem Function InitializeTrailsIntegration *********
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeTrailsIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar integra\xc3\xa7\xc3\xa3o com trilhos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar integra\xc3\xa7\xc3\xa3o com trilhos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeTrailsIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "InitializeTrailsIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeTrailsIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeTrailsIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeTrailsIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeTrailsIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execInitializeTrailsIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeTrailsIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function InitializeTrailsIntegration ***********

// ********** Begin Class AAURACRONPCGObjectiveSystem Function IsObjectiveAvailable ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics
{
	struct AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms
	{
		int32 ObjectiveIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se objetivo est\xc3\xa1 dispon\xc3\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se objetivo est\xc3\xa1 dispon\xc3\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms), &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "IsObjectiveAvailable", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execIsObjectiveAvailable)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsObjectiveAvailable(Z_Param_ObjectiveIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function IsObjectiveAvailable ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function OnAssetsLoadedComplete **************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnAssetsLoadedComplete_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback quando assets s\xc3\xa3o carregados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback quando assets s\xc3\xa3o carregados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnAssetsLoadedComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "OnAssetsLoadedComplete", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnAssetsLoadedComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnAssetsLoadedComplete_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnAssetsLoadedComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnAssetsLoadedComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execOnAssetsLoadedComplete)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnAssetsLoadedComplete();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function OnAssetsLoadedComplete ****************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function OnProceduralGenerationTimerExpired **
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para timer de gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para timer de gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "OnProceduralGenerationTimerExpired", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execOnProceduralGenerationTimerExpired)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnProceduralGenerationTimerExpired();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function OnProceduralGenerationTimerExpired ****

// ********** Begin Class AAURACRONPCGObjectiveSystem Function StartObjectiveSystem ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iniciar sistema de objetivos procedurais (do ObjectiveManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar sistema de objetivos procedurais (do ObjectiveManager)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "StartObjectiveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execStartObjectiveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartObjectiveSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function StartObjectiveSystem ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function StopObjectiveSystem *****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parar sistema de objetivos procedurais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar sistema de objetivos procedurais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "StopObjectiveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execStopObjectiveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopObjectiveSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function StopObjectiveSystem *******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function TriggerChaosIslandEvent *************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics
{
	struct AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms
	{
		int32 ChaosIslandIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar evento de Chaos Island */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar evento de Chaos Island" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ChaosIslandIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::NewProp_ChaosIslandIndex = { "ChaosIslandIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms, ChaosIslandIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::NewProp_ChaosIslandIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "TriggerChaosIslandEvent", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execTriggerChaosIslandEvent)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ChaosIslandIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerChaosIslandEvent(Z_Param_ChaosIslandIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function TriggerChaosIslandEvent ***************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateCentralAuracronIslandIntegration 
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateCentralAuracronIslandIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar integra\xc3\xa7\xc3\xa3o com Ilha Central */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar integra\xc3\xa7\xc3\xa3o com Ilha Central" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateCentralAuracronIslandIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateCentralAuracronIslandIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateCentralAuracronIslandIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateCentralAuracronIslandIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateCentralAuracronIslandIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateCentralAuracronIslandIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateCentralAuracronIslandIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCentralAuracronIslandIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateCentralAuracronIslandIntegration 

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateForEnvironment ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics
{
	struct AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms
	{
		EAURACRONEnvironmentType NewEnvironment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para novo ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para novo ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewEnvironment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment = { "NewEnvironment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms, NewEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateForEnvironment", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewEnvironment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForEnvironment(EAURACRONEnvironmentType(Z_Param_NewEnvironment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateForEnvironment ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateForMapPhase *******************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics
{
	struct AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para nova fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para nova fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateForMapPhase *********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateObjectivesTimer ***************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateObjectivesTimer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer otimizado para atualiza\xc3\xa7\xc3\xa3o de objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer otimizado para atualiza\xc3\xa7\xc3\xa3o de objetivos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateObjectivesTimer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateObjectivesTimer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateObjectivesTimer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateObjectivesTimer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateObjectivesTimer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateObjectivesTimer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateObjectivesTimer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateObjectivesTimer();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateObjectivesTimer *****************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdatePrismalFlowForEnvironment *****
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics
{
	struct AURACRONPCGObjectiveSystem_eventUpdatePrismalFlowForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar Fluxo Prismal para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar Fluxo Prismal para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventUpdatePrismalFlowForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdatePrismalFlowForEnvironment", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdatePrismalFlowForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdatePrismalFlowForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdatePrismalFlowForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePrismalFlowForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdatePrismalFlowForEnvironment *******

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdatePrismalFlowIntegration ********
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar integra\xc3\xa7\xc3\xa3o com Fluxo Prismal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar integra\xc3\xa7\xc3\xa3o com Fluxo Prismal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdatePrismalFlowIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdatePrismalFlowIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePrismalFlowIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdatePrismalFlowIntegration **********

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateSystemsForMapPhase ************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics
{
	struct AURACRONPCGObjectiveSystem_eventUpdateSystemsForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar sistemas para fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar sistemas para fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventUpdateSystemsForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateSystemsForMapPhase", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::AURACRONPCGObjectiveSystem_eventUpdateSystemsForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::AURACRONPCGObjectiveSystem_eventUpdateSystemsForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateSystemsForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSystemsForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateSystemsForMapPhase **************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateTrailsForEnvironment **********
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics
{
	struct AURACRONPCGObjectiveSystem_eventUpdateTrailsForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar trilhos para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar trilhos para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventUpdateTrailsForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateTrailsForEnvironment", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdateTrailsForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdateTrailsForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateTrailsForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTrailsForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateTrailsForEnvironment ************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateTrailsIntegration *************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar integra\xc3\xa7\xc3\xa3o com trilhos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar integra\xc3\xa7\xc3\xa3o com trilhos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateTrailsIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateTrailsIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTrailsIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateTrailsIntegration ***************

// ********** Begin Class AAURACRONPCGObjectiveSystem **********************************************
void AAURACRONPCGObjectiveSystem::StaticRegisterNativesAAURACRONPCGObjectiveSystem()
{
	UClass* Class = AAURACRONPCGObjectiveSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddAnchorEffects", &AAURACRONPCGObjectiveSystem::execAddAnchorEffects },
		{ "AddPrismalNexusEffects", &AAURACRONPCGObjectiveSystem::execAddPrismalNexusEffects },
		{ "AddStormCoreEffects", &AAURACRONPCGObjectiveSystem::execAddStormCoreEffects },
		{ "ApplyEnvironmentPrismalFlowIntegration", &AAURACRONPCGObjectiveSystem::execApplyEnvironmentPrismalFlowIntegration },
		{ "ApplyEnvironmentTrailsIntegration", &AAURACRONPCGObjectiveSystem::execApplyEnvironmentTrailsIntegration },
		{ "ApplyIslandControlEffects", &AAURACRONPCGObjectiveSystem::execApplyIslandControlEffects },
		{ "ApplyTrailPowerToActors", &AAURACRONPCGObjectiveSystem::execApplyTrailPowerToActors },
		{ "AttackObjective", &AAURACRONPCGObjectiveSystem::execAttackObjective },
		{ "CalculateIslandControlFromSectors", &AAURACRONPCGObjectiveSystem::execCalculateIslandControlFromSectors },
		{ "CalculatePrismalFlowIntensityFromObjectives", &AAURACRONPCGObjectiveSystem::execCalculatePrismalFlowIntensityFromObjectives },
		{ "CalculateTrailPowerFromObjectives", &AAURACRONPCGObjectiveSystem::execCalculateTrailPowerFromObjectives },
		{ "CaptureObjective", &AAURACRONPCGObjectiveSystem::execCaptureObjective },
		{ "ForceGenerateObjective", &AAURACRONPCGObjectiveSystem::execForceGenerateObjective },
		{ "GenerateObjectives", &AAURACRONPCGObjectiveSystem::execGenerateObjectives },
		{ "GenerateObjectivesForEnvironment", &AAURACRONPCGObjectiveSystem::execGenerateObjectivesForEnvironment },
		{ "GetAllObjectives", &AAURACRONPCGObjectiveSystem::execGetAllObjectives },
		{ "GetAllProceduralObjectives", &AAURACRONPCGObjectiveSystem::execGetAllProceduralObjectives },
		{ "GetObjectiveBuffs", &AAURACRONPCGObjectiveSystem::execGetObjectiveBuffs },
		{ "GetObjectivesByState", &AAURACRONPCGObjectiveSystem::execGetObjectivesByState },
		{ "GetObjectivesByType", &AAURACRONPCGObjectiveSystem::execGetObjectivesByType },
		{ "GetStencilValueForObjectiveType", &AAURACRONPCGObjectiveSystem::execGetStencilValueForObjectiveType },
		{ "InitializeCentralAuracronIslandIntegration", &AAURACRONPCGObjectiveSystem::execInitializeCentralAuracronIslandIntegration },
		{ "InitializeIslandSectors", &AAURACRONPCGObjectiveSystem::execInitializeIslandSectors },
		{ "InitializeObjectiveSystemAsync", &AAURACRONPCGObjectiveSystem::execInitializeObjectiveSystemAsync },
		{ "InitializePrismalFlowIntegration", &AAURACRONPCGObjectiveSystem::execInitializePrismalFlowIntegration },
		{ "InitializeTrailsIntegration", &AAURACRONPCGObjectiveSystem::execInitializeTrailsIntegration },
		{ "IsObjectiveAvailable", &AAURACRONPCGObjectiveSystem::execIsObjectiveAvailable },
		{ "OnAssetsLoadedComplete", &AAURACRONPCGObjectiveSystem::execOnAssetsLoadedComplete },
		{ "OnProceduralGenerationTimerExpired", &AAURACRONPCGObjectiveSystem::execOnProceduralGenerationTimerExpired },
		{ "StartObjectiveSystem", &AAURACRONPCGObjectiveSystem::execStartObjectiveSystem },
		{ "StopObjectiveSystem", &AAURACRONPCGObjectiveSystem::execStopObjectiveSystem },
		{ "TriggerChaosIslandEvent", &AAURACRONPCGObjectiveSystem::execTriggerChaosIslandEvent },
		{ "UpdateCentralAuracronIslandIntegration", &AAURACRONPCGObjectiveSystem::execUpdateCentralAuracronIslandIntegration },
		{ "UpdateForEnvironment", &AAURACRONPCGObjectiveSystem::execUpdateForEnvironment },
		{ "UpdateForMapPhase", &AAURACRONPCGObjectiveSystem::execUpdateForMapPhase },
		{ "UpdateObjectivesTimer", &AAURACRONPCGObjectiveSystem::execUpdateObjectivesTimer },
		{ "UpdatePrismalFlowForEnvironment", &AAURACRONPCGObjectiveSystem::execUpdatePrismalFlowForEnvironment },
		{ "UpdatePrismalFlowIntegration", &AAURACRONPCGObjectiveSystem::execUpdatePrismalFlowIntegration },
		{ "UpdateSystemsForMapPhase", &AAURACRONPCGObjectiveSystem::execUpdateSystemsForMapPhase },
		{ "UpdateTrailsForEnvironment", &AAURACRONPCGObjectiveSystem::execUpdateTrailsForEnvironment },
		{ "UpdateTrailsIntegration", &AAURACRONPCGObjectiveSystem::execUpdateTrailsIntegration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem;
UClass* AAURACRONPCGObjectiveSystem::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGObjectiveSystem;
	if (!Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGObjectiveSystem"),
			Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGObjectiveSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister()
{
	return AAURACRONPCGObjectiveSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sistema consolidado de objetivos estrat\xc3\xa9gicos para AURACRON\n * CONSOLIDADO: Combina AURACRONPCGObjectiveSystem e AURACRONPCGObjectiveManager\n * Baseado no sistema Baron/Dragon do LoL com objetivos procedurais \xc3\xbanicos\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGObjectiveSystem.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema consolidado de objetivos estrat\xc3\xa9gicos para AURACRON\nCONSOLIDADO: Combina AURACRONPCGObjectiveSystem e AURACRONPCGObjectiveManager\nBaseado no sistema Baron/Dragon do LoL com objetivos procedurais \xc3\xbanicos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveCreated_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando objetivo procedural \xc3\xa9 criado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando objetivo procedural \xc3\xa9 criado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveDestroyed_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando objetivo procedural \xc3\xa9 destru\xc3\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando objetivo procedural \xc3\xa9 destru\xc3\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveStateChanged_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando estado de objetivo muda */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando estado de objetivo muda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveCaptured_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando objetivo \xc3\xa9 capturado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando objetivo \xc3\xa9 capturado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChaosIslandEvent_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado para Chaos Island */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado para Chaos Island" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveBuffApplied_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando buff de objetivo \xc3\xa9 aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando buff de objetivo \xc3\xa9 aplicado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objectives_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Informa\xc3\xa7\xc3\xb5""es de todos os objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de todos os objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveMeshesByEnvironment_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes visuais dos objetivos por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes visuais dos objetivos por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveMeshesByType_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Malhas dos objetivos por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Malhas dos objetivos por tipo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultObjectiveMesh_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Malha padr\xc3\xa3o para objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Malha padr\xc3\xa3o para objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveCollisionComponents_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de colis\xc3\xa3o dos objetivos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de colis\xc3\xa3o dos objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerate_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve gerar automaticamente no BeginPlay */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve gerar automaticamente no BeginPlay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveProceduralObjectives_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objetivos procedurais ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objetivos procedurais ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralObjectives_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lista de todos os objetivos procedurais (alias para compatibilidade) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lista de todos os objetivos procedurais (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationConfig_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProceduralSystemActive_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o sistema procedural est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o sistema procedural est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCatchUpMechanicsActive_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se catch-up mechanics est\xc3\xa3o ativas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se catch-up mechanics est\xc3\xa3o ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoStart_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se auto-start est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se auto-start est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o sistema est\xc3\xa1 completamente inicializado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o sistema est\xc3\xa1 completamente inicializado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveAudioComponent_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de \xc3\xa1udio para feedback de objetivos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de \xc3\xa1udio para feedback de objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveAttackSound_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de ataque a objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de ataque a objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveCaptureSound_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de captura de objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de captura de objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRespawnSound_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de respawn de objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de respawn de objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowIntensity_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade atual do Fluxo Prismal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade atual do Fluxo Prismal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolarTrailPower_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Trails" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Poder atual dos Solar Trilhos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder atual dos Solar Trilhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AxisTrailPower_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Trails" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Poder atual dos Axis Trilhos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder atual dos Axis Trilhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LunarTrailPower_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Trails" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Poder atual dos Lunar Trilhos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder atual dos Lunar Trilhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CentralAuracronIslandControlPercentage_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|CentralIsland" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Porcentagem de controle da Ilha Central Auracron */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Porcentagem de controle da Ilha Central Auracron" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolarTrails_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias dos trilhos Solar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias dos trilhos Solar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AxisTrails_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias dos trilhos Axis */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias dos trilhos Axis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LunarTrails_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias dos trilhos Lunar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias dos trilhos Lunar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowReference_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia do Fluxo Prismal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia do Fluxo Prismal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CentralAuracronIslandReference_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia da Ilha Central Auracron */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia da Ilha Central Auracron" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandSectorControl_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Controle dos setores da Ilha Central - UE 5.6: TMap n\xc3\xa3o suporta replica\xc3\xa7\xc3\xa3o direta */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Controle dos setores da Ilha Central - UE 5.6: TMap n\xc3\xa3o suporta replica\xc3\xa7\xc3\xa3o direta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRespawnTimers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timers de respawn dos objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timers de respawn dos objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosIslandEventTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para eventos de Chaos Island */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para eventos de Chaos Island" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveEffectComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de efeitos para objetivos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de efeitos para objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalNexusParticleSystem_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistemas de part\xc3\xad""culas para diferentes tipos de objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistemas de part\xc3\xad""culas para diferentes tipos de objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RadiantAnchorParticleSystem_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZephyrAnchorParticleSystem_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PurgatoryAnchorParticleSystem_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultAnchorParticleSystem_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NextChaosIslandIndex_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x8dndice da pr\xc3\xb3xima Chaos Island a ativar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x8dndice da pr\xc3\xb3xima Chaos Island a ativar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralGenerationTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveIDCounter_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contador para IDs \xc3\xbanicos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contador para IDs \xc3\xbanicos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGActorReferences_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias aos atores PCG (usando utility class) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias aos atores PCG (usando utility class)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveDestroyed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveCaptured;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChaosIslandEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveBuffApplied;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Objectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveMeshesByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ObjectiveMeshesByEnvironment;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveMeshesByType_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveMeshesByType_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveMeshesByType_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ObjectiveMeshesByType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DefaultObjectiveMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveCollisionComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObjectiveCollisionComponents;
	static void NewProp_bAutoGenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerate;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveProceduralObjectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveProceduralObjectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProceduralObjectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ProceduralObjectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationConfig;
	static void NewProp_bProceduralSystemActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProceduralSystemActive;
	static void NewProp_bCatchUpMechanicsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCatchUpMechanicsActive;
	static void NewProp_bAutoStart_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoStart;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveAudioComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveAttackSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveCaptureSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveRespawnSound;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PrismalFlowIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SolarTrailPower;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AxisTrailPower;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LunarTrailPower;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CentralAuracronIslandControlPercentage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolarTrails_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SolarTrails;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AxisTrails_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AxisTrails;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LunarTrails_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LunarTrails;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PrismalFlowReference;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CentralAuracronIslandReference;
	static const UECodeGen_Private::FIntPropertyParams NewProp_IslandSectorControl_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandSectorControl_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandSectorControl_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_IslandSectorControl;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveRespawnTimers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObjectiveRespawnTimers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChaosIslandEventTimer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveEffectComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObjectiveEffectComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PrismalNexusParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RadiantAnchorParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ZephyrAnchorParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PurgatoryAnchorParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DefaultAnchorParticleSystem;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NextChaosIslandIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProceduralGenerationTimer;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIDCounter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PCGActorReferences;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddAnchorEffects, "AddAnchorEffects" }, // 2711384775
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddPrismalNexusEffects, "AddPrismalNexusEffects" }, // 2844011241
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AddStormCoreEffects, "AddStormCoreEffects" }, // 1472353448
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentPrismalFlowIntegration, "ApplyEnvironmentPrismalFlowIntegration" }, // 3383427622
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyEnvironmentTrailsIntegration, "ApplyEnvironmentTrailsIntegration" }, // 916096706
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyIslandControlEffects, "ApplyIslandControlEffects" }, // 1951798763
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ApplyTrailPowerToActors, "ApplyTrailPowerToActors" }, // 2872882825
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective, "AttackObjective" }, // 3374826152
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateIslandControlFromSectors, "CalculateIslandControlFromSectors" }, // 686424066
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculatePrismalFlowIntensityFromObjectives, "CalculatePrismalFlowIntensityFromObjectives" }, // 1462971905
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CalculateTrailPowerFromObjectives, "CalculateTrailPowerFromObjectives" }, // 1425255587
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective, "CaptureObjective" }, // 1203343576
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective, "ForceGenerateObjective" }, // 179162413
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives, "GenerateObjectives" }, // 2957183820
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment, "GenerateObjectivesForEnvironment" }, // 685515083
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives, "GetAllObjectives" }, // 2114597665
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives, "GetAllProceduralObjectives" }, // 3083709547
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs, "GetObjectiveBuffs" }, // 3956939105
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState, "GetObjectivesByState" }, // 203634975
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType, "GetObjectivesByType" }, // 3269290360
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetStencilValueForObjectiveType, "GetStencilValueForObjectiveType" }, // 922806122
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeCentralAuracronIslandIntegration, "InitializeCentralAuracronIslandIntegration" }, // 972985421
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeIslandSectors, "InitializeIslandSectors" }, // 446177837
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeObjectiveSystemAsync, "InitializeObjectiveSystemAsync" }, // 2319656150
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializePrismalFlowIntegration, "InitializePrismalFlowIntegration" }, // 4197289289
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_InitializeTrailsIntegration, "InitializeTrailsIntegration" }, // 4095252428
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable, "IsObjectiveAvailable" }, // 2977368534
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnAssetsLoadedComplete, "OnAssetsLoadedComplete" }, // 1976593097
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired, "OnProceduralGenerationTimerExpired" }, // 3323085097
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem, "StartObjectiveSystem" }, // 3307081285
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem, "StopObjectiveSystem" }, // 2940933908
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent, "TriggerChaosIslandEvent" }, // 4157970345
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateCentralAuracronIslandIntegration, "UpdateCentralAuracronIslandIntegration" }, // 1367593671
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment, "UpdateForEnvironment" }, // 3173847562
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase, "UpdateForMapPhase" }, // 3496748394
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateObjectivesTimer, "UpdateObjectivesTimer" }, // 466686441
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowForEnvironment, "UpdatePrismalFlowForEnvironment" }, // 4149270382
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdatePrismalFlowIntegration, "UpdatePrismalFlowIntegration" }, // 3608008575
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateSystemsForMapPhase, "UpdateSystemsForMapPhase" }, // 924990685
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsForEnvironment, "UpdateTrailsForEnvironment" }, // 1532041205
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateTrailsIntegration, "UpdateTrailsIntegration" }, // 1226500563
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGObjectiveSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCreated = { "OnObjectiveCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveCreated), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveCreated_MetaData), NewProp_OnObjectiveCreated_MetaData) }; // 1488299274
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveDestroyed = { "OnObjectiveDestroyed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveDestroyed), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveDestroyed_MetaData), NewProp_OnObjectiveDestroyed_MetaData) }; // 2397496712
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveStateChanged = { "OnObjectiveStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveStateChanged), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveStateChanged_MetaData), NewProp_OnObjectiveStateChanged_MetaData) }; // 3691761492
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCaptured = { "OnObjectiveCaptured", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveCaptured), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveCaptured_MetaData), NewProp_OnObjectiveCaptured_MetaData) }; // 2868719883
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnChaosIslandEvent = { "OnChaosIslandEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnChaosIslandEvent), Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChaosIslandEvent_MetaData), NewProp_OnChaosIslandEvent_MetaData) }; // 1707412193
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveBuffApplied = { "OnObjectiveBuffApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveBuffApplied), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveBuffApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveBuffApplied_MetaData), NewProp_OnObjectiveBuffApplied_MetaData) }; // 3649489888
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives_Inner = { "Objectives", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 165882
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives = { "Objectives", nullptr, (EPropertyFlags)0x0020088000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, Objectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objectives_MetaData), NewProp_Objectives_MetaData) }; // 165882
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_ValueProp = { "ObjectiveMeshesByEnvironment", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray, METADATA_PARAMS(0, nullptr) }; // 3350711660
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp = { "ObjectiveMeshesByEnvironment_Key", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment = { "ObjectiveMeshesByEnvironment", nullptr, (EPropertyFlags)0x0020088000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveMeshesByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveMeshesByEnvironment_MetaData), NewProp_ObjectiveMeshesByEnvironment_MetaData) }; // 2509470107 3350711660
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType_ValueProp = { "ObjectiveMeshesByType", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType_Key_KeyProp = { "ObjectiveMeshesByType_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2271266485
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType = { "ObjectiveMeshesByType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveMeshesByType), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveMeshesByType_MetaData), NewProp_ObjectiveMeshesByType_MetaData) }; // 2271266485
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_DefaultObjectiveMesh = { "DefaultObjectiveMesh", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, DefaultObjectiveMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultObjectiveMesh_MetaData), NewProp_DefaultObjectiveMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents_Inner = { "ObjectiveCollisionComponents", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents = { "ObjectiveCollisionComponents", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveCollisionComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveCollisionComponents_MetaData), NewProp_ObjectiveCollisionComponents_MetaData) };
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bAutoGenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate = { "bAutoGenerate", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerate_MetaData), NewProp_bAutoGenerate_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 2509470107
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives_Inner = { "ActiveProceduralObjectives", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 3242810244
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives = { "ActiveProceduralObjectives", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ActiveProceduralObjectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveProceduralObjectives_MetaData), NewProp_ActiveProceduralObjectives_MetaData) }; // 3242810244
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralObjectives_Inner = { "ProceduralObjectives", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 3242810244
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralObjectives = { "ProceduralObjectives", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ProceduralObjectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralObjectives_MetaData), NewProp_ProceduralObjectives_MetaData) }; // 3242810244
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_GenerationConfig = { "GenerationConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, GenerationConfig), Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationConfig_MetaData), NewProp_GenerationConfig_MetaData) }; // 1527385679
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bProceduralSystemActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive = { "bProceduralSystemActive", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProceduralSystemActive_MetaData), NewProp_bProceduralSystemActive_MetaData) };
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bCatchUpMechanicsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive = { "bCatchUpMechanicsActive", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCatchUpMechanicsActive_MetaData), NewProp_bCatchUpMechanicsActive_MetaData) };
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bAutoStart = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart = { "bAutoStart", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoStart_MetaData), NewProp_bAutoStart_MetaData) };
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000020, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveAudioComponent = { "ObjectiveAudioComponent", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveAudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveAudioComponent_MetaData), NewProp_ObjectiveAudioComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveAttackSound = { "ObjectiveAttackSound", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveAttackSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveAttackSound_MetaData), NewProp_ObjectiveAttackSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCaptureSound = { "ObjectiveCaptureSound", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveCaptureSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveCaptureSound_MetaData), NewProp_ObjectiveCaptureSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnSound = { "ObjectiveRespawnSound", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveRespawnSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRespawnSound_MetaData), NewProp_ObjectiveRespawnSound_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PrismalFlowIntensity = { "PrismalFlowIntensity", nullptr, (EPropertyFlags)0x0040000000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, PrismalFlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowIntensity_MetaData), NewProp_PrismalFlowIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_SolarTrailPower = { "SolarTrailPower", nullptr, (EPropertyFlags)0x0040000000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, SolarTrailPower), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolarTrailPower_MetaData), NewProp_SolarTrailPower_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_AxisTrailPower = { "AxisTrailPower", nullptr, (EPropertyFlags)0x0040000000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, AxisTrailPower), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AxisTrailPower_MetaData), NewProp_AxisTrailPower_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_LunarTrailPower = { "LunarTrailPower", nullptr, (EPropertyFlags)0x0040000000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, LunarTrailPower), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LunarTrailPower_MetaData), NewProp_LunarTrailPower_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CentralAuracronIslandControlPercentage = { "CentralAuracronIslandControlPercentage", nullptr, (EPropertyFlags)0x0040000000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, CentralAuracronIslandControlPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CentralAuracronIslandControlPercentage_MetaData), NewProp_CentralAuracronIslandControlPercentage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_SolarTrails_Inner = { "SolarTrails", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGTrail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_SolarTrails = { "SolarTrails", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, SolarTrails), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolarTrails_MetaData), NewProp_SolarTrails_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_AxisTrails_Inner = { "AxisTrails", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGTrail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_AxisTrails = { "AxisTrails", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, AxisTrails), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AxisTrails_MetaData), NewProp_AxisTrails_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_LunarTrails_Inner = { "LunarTrails", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAURACRONPCGTrail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_LunarTrails = { "LunarTrails", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, LunarTrails), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LunarTrails_MetaData), NewProp_LunarTrails_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PrismalFlowReference = { "PrismalFlowReference", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, PrismalFlowReference), Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowReference_MetaData), NewProp_PrismalFlowReference_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CentralAuracronIslandReference = { "CentralAuracronIslandReference", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, CentralAuracronIslandReference), Z_Construct_UClass_AAURACRONPCGIsland_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CentralAuracronIslandReference_MetaData), NewProp_CentralAuracronIslandReference_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl_ValueProp = { "IslandSectorControl", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl_Key_KeyProp = { "IslandSectorControl_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONIslandSector, METADATA_PARAMS(0, nullptr) }; // 2158473133
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl = { "IslandSectorControl", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, IslandSectorControl), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandSectorControl_MetaData), NewProp_IslandSectorControl_MetaData) }; // 2158473133
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers_Inner = { "ObjectiveRespawnTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(0, nullptr) }; // 3834150579
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers = { "ObjectiveRespawnTimers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveRespawnTimers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRespawnTimers_MetaData), NewProp_ObjectiveRespawnTimers_MetaData) }; // 3834150579
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ChaosIslandEventTimer = { "ChaosIslandEventTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ChaosIslandEventTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosIslandEventTimer_MetaData), NewProp_ChaosIslandEventTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveEffectComponents_Inner = { "ObjectiveEffectComponents", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveEffectComponents = { "ObjectiveEffectComponents", nullptr, (EPropertyFlags)0x0040008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveEffectComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveEffectComponents_MetaData), NewProp_ObjectiveEffectComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PrismalNexusParticleSystem = { "PrismalNexusParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, PrismalNexusParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalNexusParticleSystem_MetaData), NewProp_PrismalNexusParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_RadiantAnchorParticleSystem = { "RadiantAnchorParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, RadiantAnchorParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RadiantAnchorParticleSystem_MetaData), NewProp_RadiantAnchorParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ZephyrAnchorParticleSystem = { "ZephyrAnchorParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ZephyrAnchorParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZephyrAnchorParticleSystem_MetaData), NewProp_ZephyrAnchorParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PurgatoryAnchorParticleSystem = { "PurgatoryAnchorParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, PurgatoryAnchorParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PurgatoryAnchorParticleSystem_MetaData), NewProp_PurgatoryAnchorParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_DefaultAnchorParticleSystem = { "DefaultAnchorParticleSystem", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, DefaultAnchorParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultAnchorParticleSystem_MetaData), NewProp_DefaultAnchorParticleSystem_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_NextChaosIslandIndex = { "NextChaosIslandIndex", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, NextChaosIslandIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NextChaosIslandIndex_MetaData), NewProp_NextChaosIslandIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralGenerationTimer = { "ProceduralGenerationTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ProceduralGenerationTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralGenerationTimer_MetaData), NewProp_ProceduralGenerationTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveIDCounter = { "ObjectiveIDCounter", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveIDCounter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveIDCounter_MetaData), NewProp_ObjectiveIDCounter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PCGActorReferences = { "PCGActorReferences", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, PCGActorReferences), Z_Construct_UScriptStruct_FAURACRONPCGActorReferences, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGActorReferences_MetaData), NewProp_PCGActorReferences_MetaData) }; // 2729947204
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCaptured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnChaosIslandEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveBuffApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_DefaultObjectiveMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralObjectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_GenerationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveAudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveAttackSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCaptureSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PrismalFlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_SolarTrailPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_AxisTrailPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_LunarTrailPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CentralAuracronIslandControlPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_SolarTrails_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_SolarTrails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_AxisTrails_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_AxisTrails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_LunarTrails_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_LunarTrails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PrismalFlowReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CentralAuracronIslandReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_IslandSectorControl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ChaosIslandEventTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveEffectComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveEffectComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PrismalNexusParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_RadiantAnchorParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ZephyrAnchorParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PurgatoryAnchorParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_DefaultAnchorParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_NextChaosIslandIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralGenerationTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveIDCounter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PCGActorReferences,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::ClassParams = {
	&AAURACRONPCGObjectiveSystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.OuterSingleton, Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGObjectiveSystem::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_ActiveProceduralObjectives(TEXT("ActiveProceduralObjectives"));
	static FName Name_ProceduralObjectives(TEXT("ProceduralObjectives"));
	static FName Name_bProceduralSystemActive(TEXT("bProceduralSystemActive"));
	static FName Name_bIsInitialized(TEXT("bIsInitialized"));
	static FName Name_PrismalFlowIntensity(TEXT("PrismalFlowIntensity"));
	static FName Name_SolarTrailPower(TEXT("SolarTrailPower"));
	static FName Name_AxisTrailPower(TEXT("AxisTrailPower"));
	static FName Name_LunarTrailPower(TEXT("LunarTrailPower"));
	static FName Name_CentralAuracronIslandControlPercentage(TEXT("CentralAuracronIslandControlPercentage"));
	const bool bIsValid = true
		&& Name_ActiveProceduralObjectives == ClassReps[(int32)ENetFields_Private::ActiveProceduralObjectives].Property->GetFName()
		&& Name_ProceduralObjectives == ClassReps[(int32)ENetFields_Private::ProceduralObjectives].Property->GetFName()
		&& Name_bProceduralSystemActive == ClassReps[(int32)ENetFields_Private::bProceduralSystemActive].Property->GetFName()
		&& Name_bIsInitialized == ClassReps[(int32)ENetFields_Private::bIsInitialized].Property->GetFName()
		&& Name_PrismalFlowIntensity == ClassReps[(int32)ENetFields_Private::PrismalFlowIntensity].Property->GetFName()
		&& Name_SolarTrailPower == ClassReps[(int32)ENetFields_Private::SolarTrailPower].Property->GetFName()
		&& Name_AxisTrailPower == ClassReps[(int32)ENetFields_Private::AxisTrailPower].Property->GetFName()
		&& Name_LunarTrailPower == ClassReps[(int32)ENetFields_Private::LunarTrailPower].Property->GetFName()
		&& Name_CentralAuracronIslandControlPercentage == ClassReps[(int32)ENetFields_Private::CentralAuracronIslandControlPercentage].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGObjectiveSystem"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGObjectiveSystem);
AAURACRONPCGObjectiveSystem::~AAURACRONPCGObjectiveSystem() {}
// ********** End Class AAURACRONPCGObjectiveSystem ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONObjectiveInfo::StaticStruct, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewStructOps, TEXT("AURACRONObjectiveInfo"), &Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONObjectiveInfo), 165882U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGObjectiveSystem, AAURACRONPCGObjectiveSystem::StaticClass, TEXT("AAURACRONPCGObjectiveSystem"), &Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGObjectiveSystem), 1115821397U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_2160644790(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
