{"Version": "1.2", "Data": {"Source": "c:\\auracron\\source\\auracron\\private\\pcg\\auracronpcgchaosportal.cpp", "ProvidedModule": "", "PCH": "c:\\auracron\\intermediate\\build\\win64\\x64\\auracron\\development\\engine\\sharedpch.engine.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\auracron\\intermediate\\build\\win64\\x64\\unrealgame\\development\\auracron\\definitions.auracron.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgchaosportal.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronmapmeasurements.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronmapmeasurements.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronpcgchaosportal.generated.h", "c:\\auracron\\source\\auracron\\public\\data\\auracronenums.h", "c:\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracronenums.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaradefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarascalabilitystate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagarascalabilitystate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratickbehaviorenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaratickbehaviorenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarauserredirectionparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagaracore\\uht\\niagaracore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaratypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagarauserredirectionparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaravariant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaravariant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstats.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\particlesystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\emitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\emitter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\statestream\\particlesystemstatestreamhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\particlesystemstatestreamhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\particlesystemcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaracomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarafunctionlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponentpool.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\weakfieldptr.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponentpoolmethodenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaracomponentpoolmethodenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratyperegistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaracommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaracomponentpool.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\vectorvm\\public\\vectorvm.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\vectorvm\\uht\\vectorvm.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagarasystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraassettagdefinitions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraassettagdefinitions.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetcompileddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaradatasetcompileddata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetaccessor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraeffecttype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ingameperformancetracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraplatformset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraplatformset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraperfbaseline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstatsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraperfbaseline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationrule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaravalidationrule.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationruleset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaravalidationruleset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraeffecttype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraemitterhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraemitterhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaramessagestore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaramessagestore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparametercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracompilehash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagaracore\\uht\\niagaracompilehash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraparametercollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparameterdefinitionssubscriber.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterdefinitionsdelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagaraparameterdefinitionssubscriber.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\fxbudget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagarasystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealgame\\inc\\niagara\\uht\\niagarafunctionlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\pointlightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\locallightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponentbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\lightcomponentbase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\lightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\locallightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\pointlightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\audiocomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audio\\soundparametercontrollerinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\soundparametercontrollerinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audioextensions\\public\\iaudioparametertransmitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerquantizedcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerclock.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\quartzmetronome.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscription.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscriptiontoken.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzinterfaces.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\audiocomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\spherecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\assetregistry\\arfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\character.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementreplication.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\serialization\\irisobjectreferencepackagemap.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\iriscore\\uht\\irisobjectreferencepackagemap.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\charactermovementreplication.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\rootmotionsource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\rootmotionsource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\character.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\iostoreondemand.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\sharedstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandhostgroup.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandtoc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\experimental\\streamablemanagererror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanagertypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\assetmanagertypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformchunkinstall.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\contentencryptionconfig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\assetmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\engineutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\net\\unrealnetwork.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\propertyconditions\\propertyconditions.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}